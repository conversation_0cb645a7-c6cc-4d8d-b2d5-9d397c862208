"""
AI Tools for Unreal Engine MCP Server

This module provides AI-related tools that are 100% compatible with the 
C++ implementations in UnrealMCPAICommands.cpp
"""

import logging
import json
from typing import Dict, List, Any
from mcp.server.fastmcp import FastMCP

# Set up logging
logger = logging.getLogger("UnrealMCP")

def register_ai_tools(mcp: FastMCP):
    """Register AI tools with the MCP server - 100% compatible with C++ implementations."""

    @mcp.tool()
    def create_ai_learning_pipeline(
        layer_name: str,
        learning_type: str = "reinforcement",
        model_config_json: str = "",
        training_data_path: str = ""
    ) -> Dict[str, Any]:
        """
        Create an AI learning pipeline for adaptive AI.
        Compatible with HandleCreateAILearningPipeline in C++.

        Args:
            layer_name: Name of the AI layer (required)
            learning_type: Type of learning (reinforcement, supervised, unsupervised, deep_learning)
            model_config_json: ML model configuration as JSON string (optional)
            training_data_path: Path to training data (optional)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "layer_name": layer_name,
                "learning_type": learning_type,
                "training_data_path": training_data_path
            }
            
            # Add model_config as JSON object if provided
            if model_config_json and model_config_json.strip():
                try:
                    params["model_config"] = json.loads(model_config_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for model_config"}

            response = unreal.send_command("create_ai_learning_pipeline", params)
            return response or {"success": True, "message": "AI learning pipeline created"}

        except Exception as e:
            logger.error(f"Error creating AI learning pipeline: {e}")
            return {"success": False, "message": f"Error creating AI learning pipeline: {e}"}

    @mcp.tool()
    def configure_adaptive_behavior(
        layer_name: str,
        behavior_type: str = "adaptive",
        adaptation_rules_json: str = "",
        response_threshold: float = 0.7
    ) -> Dict[str, Any]:
        """
        Configure adaptive behaviors for NPCs.
        Compatible with HandleConfigureAdaptiveBehavior in C++.
        
        Args:
            layer_name: Name of the AI layer (required)
            behavior_type: Type of behavior (adaptive, reactive, proactive)
            adaptation_rules_json: List of adaptation rules as JSON string (optional)
            response_threshold: Response threshold for adaptations (0.0-1.0)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "layer_name": layer_name,
                "behavior_type": behavior_type,
                "response_threshold": response_threshold
            }
            
            # Add adaptation_rules as array if provided
            if adaptation_rules_json and adaptation_rules_json.strip():
                try:
                    params["adaptation_rules"] = json.loads(adaptation_rules_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for adaptation_rules"}

            response = unreal.send_command("configure_adaptive_behavior", params)
            return response or {"success": True, "message": "Adaptive behavior configured"}

        except Exception as e:
            logger.error(f"Error configuring adaptive behavior: {e}")
            return {"success": False, "message": f"Error configuring adaptive behavior: {e}"}

    @mcp.tool()
    def setup_dynamic_spawn_system(
        layer_name: str,
        spawn_rules_json: str = "",
        difficulty_scaling_json: str = "",
        population_limits_json: str = ""
    ) -> Dict[str, Any]:
        """
        Setup AI-driven dynamic spawning system.
        Compatible with HandleSetupDynamicSpawnSystem in C++.
        
        Args:
            layer_name: Name of the AI layer (required)
            spawn_rules_json: Spawn rules configuration as JSON string (optional)
            difficulty_scaling_json: Difficulty scaling parameters as JSON string (optional)
            population_limits_json: Population limits configuration as JSON string (optional)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "layer_name": layer_name
            }
            
            # Add optional parameters if provided
            if spawn_rules_json and spawn_rules_json.strip():
                try:
                    params["spawn_rules"] = json.loads(spawn_rules_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for spawn_rules"}

            if difficulty_scaling_json and difficulty_scaling_json.strip():
                try:
                    params["difficulty_scaling"] = json.loads(difficulty_scaling_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for difficulty_scaling"}

            if population_limits_json and population_limits_json.strip():
                try:
                    params["population_limits"] = json.loads(population_limits_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for population_limits"}

            response = unreal.send_command("setup_dynamic_spawn_system", params)
            return response or {"success": True, "message": "Dynamic spawn system setup completed"}

        except Exception as e:
            logger.error(f"Error setting up dynamic spawn system: {e}")
            return {"success": False, "message": f"Error setting up dynamic spawn system: {e}"}

    @mcp.tool()
    def create_ai_decision_tree(
        layer_name: str,
        tree_name: str,
        decision_nodes_json: str = "",
        learning_enabled: bool = True
    ) -> Dict[str, Any]:
        """
        Create adaptive decision tree for AI.
        Compatible with HandleCreateAIDecisionTree in C++.

        Args:
            layer_name: Name of the AI layer (required)
            tree_name: Name of the decision tree (required)
            decision_nodes_json: Decision nodes configuration as JSON string (optional)
            learning_enabled: Enable learning for the decision tree
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "layer_name": layer_name,
                "tree_name": tree_name,
                "learning_enabled": learning_enabled
            }

            # Add decision_nodes if provided
            if decision_nodes_json and decision_nodes_json.strip():
                try:
                    params["decision_nodes"] = json.loads(decision_nodes_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for decision_nodes"}

            response = unreal.send_command("create_ai_decision_tree", params)
            return response or {"success": True, "message": "AI decision tree created"}

        except Exception as e:
            logger.error(f"Error creating AI decision tree: {e}")
            return {"success": False, "message": f"Error creating AI decision tree: {e}"}

    @mcp.tool()
    def configure_special_events(
        layer_name: str,
        event_triggers_json: str = "",
        event_responses_json: str = "",
        adaptive_scaling: bool = True
    ) -> Dict[str, Any]:
        """
        Configure adaptive special events.
        Compatible with HandleConfigureSpecialEvents in C++.

        Args:
            layer_name: Name of the AI layer (required)
            event_triggers_json: Event triggers configuration as JSON string (optional)
            event_responses_json: Event responses configuration as JSON string (optional)
            adaptive_scaling: Enable adaptive scaling for events
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "layer_name": layer_name,
                "adaptive_scaling": adaptive_scaling
            }

            # Add event_triggers if provided
            if event_triggers_json and event_triggers_json.strip():
                try:
                    params["event_triggers"] = json.loads(event_triggers_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for event_triggers"}

            # Add event_responses if provided
            if event_responses_json and event_responses_json.strip():
                try:
                    params["event_responses"] = json.loads(event_responses_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for event_responses"}

            response = unreal.send_command("configure_special_events", params)
            return response or {"success": True, "message": "Special events configured"}

        except Exception as e:
            logger.error(f"Error configuring special events: {e}")
            return {"success": False, "message": f"Error configuring special events: {e}"}

    @mcp.tool()
    def setup_ai_communication_system(
        layer_name: str,
        communication_range: float = 1000.0,
        message_types_json: str = "",
        learning_from_communication: bool = True
    ) -> Dict[str, Any]:
        """
        Setup AI communication system for NPCs.
        Compatible with HandleSetupAICommunicationSystem in C++.

        Args:
            layer_name: Name of the AI layer (required)
            communication_range: Communication range in units
            message_types_json: Message types configuration as JSON string (optional)
            learning_from_communication: Enable learning from communication
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "layer_name": layer_name,
                "communication_range": communication_range,
                "learning_from_communication": learning_from_communication
            }

            # Add message_types if provided
            if message_types_json and message_types_json.strip():
                try:
                    params["message_types"] = json.loads(message_types_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for message_types"}

            response = unreal.send_command("setup_ai_communication_system", params)
            return response or {"success": True, "message": "AI communication system setup"}

        except Exception as e:
            logger.error(f"Error setting up AI communication system: {e}")
            return {"success": False, "message": f"Error setting up AI communication system: {e}"}

    @mcp.tool()
    def optimize_ai_performance(
        layer_name: str,
        optimization_settings_json: str = "",
        performance_targets_json: str = ""
    ) -> Dict[str, Any]:
        """
        Optimize AI system performance.
        Compatible with HandleOptimizeAIPerformance in C++.

        Args:
            layer_name: Name of the AI layer (required)
            optimization_settings_json: Optimization settings as JSON string (optional)
            performance_targets_json: Performance targets as JSON string (optional)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "layer_name": layer_name
            }

            # Add optimization_settings if provided
            if optimization_settings_json and optimization_settings_json.strip():
                try:
                    params["optimization_settings"] = json.loads(optimization_settings_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for optimization_settings"}

            # Add performance_targets if provided
            if performance_targets_json and performance_targets_json.strip():
                try:
                    params["performance_targets"] = json.loads(performance_targets_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for performance_targets"}

            response = unreal.send_command("optimize_ai_performance", params)
            return response or {"success": True, "message": "AI performance optimized"}

        except Exception as e:
            logger.error(f"Error optimizing AI performance: {e}")
            return {"success": False, "message": f"Error optimizing AI performance: {e}"}

    @mcp.tool()
    def get_ai_system_status(
        layer_name: str
    ) -> Dict[str, Any]:
        """
        Get status of AI system.
        Compatible with HandleGetAISystemStatus in C++.

        Args:
            layer_name: Name of the AI layer (required)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "layer_name": layer_name
            }

            response = unreal.send_command("get_ai_system_status", params)
            return response or {"success": True, "message": "AI system status retrieved"}

        except Exception as e:
            logger.error(f"Error getting AI system status: {e}")
            return {"success": False, "message": f"Error getting AI system status: {e}"}

    @mcp.tool()
    def configure_player_profiling(
        layer_name: str,
        profiling_metrics_json: str = "",
        analysis_depth: float = 1.0,
        learning_rate: float = 0.1
    ) -> Dict[str, Any]:
        """
        Configure player profiling for AI adaptation.
        Compatible with HandleConfigurePlayerProfiling in C++.

        Args:
            layer_name: Name of the AI layer (required)
            profiling_metrics_json: Profiling metrics configuration as JSON string (optional)
            analysis_depth: Depth of analysis (0.0-2.0)
            learning_rate: Learning rate for profiling (0.0-1.0)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "layer_name": layer_name,
                "analysis_depth": analysis_depth,
                "learning_rate": learning_rate
            }

            # Add profiling_metrics if provided
            if profiling_metrics_json and profiling_metrics_json.strip():
                try:
                    params["profiling_metrics"] = json.loads(profiling_metrics_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for profiling_metrics"}

            response = unreal.send_command("configure_player_profiling", params)
            return response or {"success": True, "message": "Player profiling configured"}

        except Exception as e:
            logger.error(f"Error configuring player profiling: {e}")
            return {"success": False, "message": f"Error configuring player profiling: {e}"}

    @mcp.tool()
    def setup_ai_memory_system(
        layer_name: str,
        memory_types_json: str = "",
        memory_duration_json: str = "",
        shared_memory: bool = True
    ) -> Dict[str, Any]:
        """
        Setup AI memory system for NPCs.
        Compatible with HandleSetupAIMemorySystem in C++.

        Args:
            layer_name: Name of the AI layer (required)
            memory_types_json: Memory types configuration as JSON string (optional)
            memory_duration_json: Memory duration settings as JSON string (optional)
            shared_memory: Enable shared memory between NPCs
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "layer_name": layer_name,
                "shared_memory": shared_memory
            }

            # Add memory_types if provided
            if memory_types_json and memory_types_json.strip():
                try:
                    params["memory_types"] = json.loads(memory_types_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for memory_types"}

            # Add memory_duration if provided
            if memory_duration_json and memory_duration_json.strip():
                try:
                    params["memory_duration"] = json.loads(memory_duration_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for memory_duration"}

            response = unreal.send_command("setup_ai_memory_system", params)
            return response or {"success": True, "message": "AI memory system setup"}

        except Exception as e:
            logger.error(f"Error setting up AI memory system: {e}")
            return {"success": False, "message": f"Error setting up AI memory system: {e}"}

    @mcp.tool()
    def debug_ai_system(
        layer_name: str,
        debug_options_json: str = "",
        visualization_enabled: bool = True
    ) -> Dict[str, Any]:
        """
        Debug AI system with visualization and logging.
        Compatible with HandleDebugAISystem in C++.

        Args:
            layer_name: Name of the AI layer (required)
            debug_options_json: Debug options configuration as JSON string (optional)
            visualization_enabled: Enable visual debugging
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "layer_name": layer_name,
                "visualization_enabled": visualization_enabled
            }

            # Add debug_options if provided
            if debug_options_json and debug_options_json.strip():
                try:
                    params["debug_options"] = json.loads(debug_options_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for debug_options"}

            response = unreal.send_command("debug_ai_system", params)
            return response or {"success": True, "message": "AI system debug activated"}

        except Exception as e:
            logger.error(f"Error debugging AI system: {e}")
            return {"success": False, "message": f"Error debugging AI system: {e}"}

    @mcp.tool()
    def validate_ai_setup(
        layer_name: str,
        validation_rules_json: str = ""
    ) -> Dict[str, Any]:
        """
        Validate AI system configuration and setup.
        Compatible with HandleValidateAISetup in C++.

        Args:
            layer_name: Name of the AI layer (required)
            validation_rules_json: Validation rules configuration as JSON string (optional)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "layer_name": layer_name
            }

            # Add validation_rules if provided
            if validation_rules_json and validation_rules_json.strip():
                try:
                    params["validation_rules"] = json.loads(validation_rules_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for validation_rules"}

            response = unreal.send_command("validate_ai_setup", params)
            return response or {"success": True, "message": "AI setup validation completed"}

        except Exception as e:
            logger.error(f"Error validating AI setup: {e}")
            return {"success": False, "message": f"Error validating AI setup: {e}"}
