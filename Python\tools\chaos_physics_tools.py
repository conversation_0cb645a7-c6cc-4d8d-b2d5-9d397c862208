"""
Chaos Physics System Tools for Unreal Engine MCP Server

This module provides comprehensive Chaos Physics tools that are 100% compatible with the 
C++ implementations in UnrealMCPChaosPhysicsCommands.cpp. Based on UE 5.6 official documentation.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP

# Set up logging
logger = logging.getLogger("UnrealMCP")

def register_chaos_physics_tools(mcp: FastMCP):
    """Register Chaos Physics tools with the MCP server - 100% compatible with C++ implementations."""

    @mcp.tool()
    def create_geometry_collection(
        collection_name: str,
        collection_path: str = "/Game/Physics/GeometryCollections",
        source_meshes: str = "",
        auto_cluster: bool = True
    ) -> Dict[str, Any]:
        """
        Create a new Geometry Collection for Chaos Destruction.
        Compatible with HandleCreateGeometryCollection in C++.
        
        Args:
            collection_name: Name of the geometry collection (required)
            collection_path: Content browser path for the collection
            source_meshes: Comma-separated paths to source static meshes
            auto_cluster: Automatically cluster geometry pieces
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "collection_name": collection_name,
                "collection_path": collection_path,
                "source_meshes": source_meshes,
                "auto_cluster": auto_cluster
            }

            response = unreal.send_command("create_geometry_collection", params)
            return response or {"success": True, "message": "Geometry collection created successfully"}

        except Exception as e:
            logger.error(f"Error creating geometry collection: {e}")
            return {"success": False, "message": f"Error creating geometry collection: {e}"}

    @mcp.tool()
    def fracture_geometry_collection(
        collection_path: str,
        fracture_method: str = "Voronoi",
        fracture_count: int = 10,
        random_seed: int = 42
    ) -> Dict[str, Any]:
        """
        Fracture a Geometry Collection using various methods.
        Compatible with HandleFractureGeometryCollection in C++.
        
        Args:
            collection_path: Path to the geometry collection (required)
            fracture_method: Fracture method (Voronoi, Uniform, Clustered, etc.)
            fracture_count: Number of fracture pieces
            random_seed: Random seed for fracturing
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "collection_path": collection_path,
                "fracture_method": fracture_method,
                "fracture_count": fracture_count,
                "random_seed": random_seed
            }

            response = unreal.send_command("fracture_geometry_collection", params)
            return response or {"success": True, "message": "Geometry collection fractured successfully"}

        except Exception as e:
            logger.error(f"Error fracturing geometry collection: {e}")
            return {"success": False, "message": f"Error fracturing geometry collection: {e}"}

    @mcp.tool()
    def create_chaos_physics_field(
        field_name: str,
        field_type: str,
        field_location: str = "0,0,0",
        field_radius: float = 500.0,
        field_strength: float = 1000.0
    ) -> Dict[str, Any]:
        """
        Create a Chaos Physics Field for dynamic force application.
        Compatible with HandleCreateChaosPhysicsField in C++.
        
        Args:
            field_name: Name of the physics field (required)
            field_type: Type of field (RadialForce, DirectionalForce, Noise, etc.)
            field_location: Location as "X,Y,Z" string
            field_radius: Radius of field influence
            field_strength: Strength of the field effect
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "field_name": field_name,
                "field_type": field_type,
                "field_location": field_location,
                "field_radius": field_radius,
                "field_strength": field_strength
            }

            response = unreal.send_command("create_chaos_physics_field", params)
            return response or {"success": True, "message": "Chaos physics field created successfully"}

        except Exception as e:
            logger.error(f"Error creating chaos physics field: {e}")
            return {"success": False, "message": f"Error creating chaos physics field: {e}"}

    @mcp.tool()
    def spawn_destructible_actor(
        collection_path: str,
        spawn_location: str = "0,0,0",
        spawn_rotation: str = "0,0,0",
        enable_damage: bool = True,
        damage_threshold: float = 100.0
    ) -> Dict[str, Any]:
        """
        Spawn a destructible actor from a Geometry Collection.
        Compatible with HandleSpawnDestructibleActor in C++.
        
        Args:
            collection_path: Path to the geometry collection (required)
            spawn_location: Spawn location as "X,Y,Z" string
            spawn_rotation: Spawn rotation as "X,Y,Z" string (degrees)
            enable_damage: Enable damage-based destruction
            damage_threshold: Damage threshold for destruction
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "collection_path": collection_path,
                "spawn_location": spawn_location,
                "spawn_rotation": spawn_rotation,
                "enable_damage": enable_damage,
                "damage_threshold": damage_threshold
            }

            response = unreal.send_command("spawn_destructible_actor", params)
            return response or {"success": True, "message": "Destructible actor spawned successfully"}

        except Exception as e:
            logger.error(f"Error spawning destructible actor: {e}")
            return {"success": False, "message": f"Error spawning destructible actor: {e}"}

    @mcp.tool()
    def apply_chaos_damage(
        actor_name: str,
        damage_location: str = "0,0,0",
        damage_radius: float = 200.0,
        damage_amount: float = 500.0,
        impulse_strength: float = 1000.0
    ) -> Dict[str, Any]:
        """
        Apply damage to a Chaos destructible actor.
        Compatible with HandleApplyChaosDamage in C++.
        
        Args:
            actor_name: Name of the destructible actor (required)
            damage_location: Damage location as "X,Y,Z" string
            damage_radius: Radius of damage area
            damage_amount: Amount of damage to apply
            impulse_strength: Impulse strength for fragments
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "actor_name": actor_name,
                "damage_location": damage_location,
                "damage_radius": damage_radius,
                "damage_amount": damage_amount,
                "impulse_strength": impulse_strength
            }

            response = unreal.send_command("apply_chaos_damage", params)
            return response or {"success": True, "message": "Chaos damage applied successfully"}

        except Exception as e:
            logger.error(f"Error applying chaos damage: {e}")
            return {"success": False, "message": f"Error applying chaos damage: {e}"}

    @mcp.tool()
    def create_fluid_simulation(
        simulation_name: str,
        simulation_path: str = "/Game/Physics/Fluids",
        fluid_type: str = "Water",
        grid_resolution: int = 64,
        viscosity: float = 1.0
    ) -> Dict[str, Any]:
        """
        Create a fluid simulation using Chaos Physics.
        Compatible with HandleCreateFluidSimulation in C++.
        
        Args:
            simulation_name: Name of the fluid simulation (required)
            simulation_path: Content browser path for the simulation
            fluid_type: Type of fluid (Water, Oil, Gas, Custom)
            grid_resolution: Grid resolution for simulation
            viscosity: Fluid viscosity value
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "simulation_name": simulation_name,
                "simulation_path": simulation_path,
                "fluid_type": fluid_type,
                "grid_resolution": grid_resolution,
                "viscosity": viscosity
            }

            response = unreal.send_command("create_fluid_simulation", params)
            return response or {"success": True, "message": "Fluid simulation created successfully"}

        except Exception as e:
            logger.error(f"Error creating fluid simulation: {e}")
            return {"success": False, "message": f"Error creating fluid simulation: {e}"}

    @mcp.tool()
    def configure_chaos_solver(
        solver_name: str = "DefaultChaosSolver",
        gravity: str = "0,0,-980",
        damping: float = 0.1,
        max_substeps: int = 5,
        enable_multithreading: bool = True
    ) -> Dict[str, Any]:
        """
        Configure Chaos Physics solver settings.
        Compatible with HandleConfigureChaosSolver in C++.
        
        Args:
            solver_name: Name of the Chaos solver
            gravity: Gravity vector as "X,Y,Z" string
            damping: Global damping value
            max_substeps: Maximum physics substeps
            enable_multithreading: Enable multi-threading for physics
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "solver_name": solver_name,
                "gravity": gravity,
                "damping": damping,
                "max_substeps": max_substeps,
                "enable_multithreading": enable_multithreading
            }

            response = unreal.send_command("configure_chaos_solver", params)
            return response or {"success": True, "message": "Chaos solver configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring chaos solver: {e}")
            return {"success": False, "message": f"Error configuring chaos solver: {e}"}

    @mcp.tool()
    def create_cloth_simulation(
        cloth_name: str,
        mesh_path: str,
        cloth_path: str = "/Game/Physics/Cloth",
        stiffness: float = 1.0,
        damping: float = 0.1
    ) -> Dict[str, Any]:
        """
        Create a cloth simulation using Chaos Physics.
        Compatible with HandleCreateClothSimulation in C++.

        Args:
            cloth_name: Name of the cloth simulation (required)
            mesh_path: Path to the source mesh (required)
            cloth_path: Content browser path for the cloth asset
            stiffness: Cloth stiffness value
            damping: Cloth damping value
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "cloth_name": cloth_name,
                "mesh_path": mesh_path,
                "cloth_path": cloth_path,
                "stiffness": stiffness,
                "damping": damping
            }

            response = unreal.send_command("create_cloth_simulation", params)
            return response or {"success": True, "message": "Cloth simulation created successfully"}

        except Exception as e:
            logger.error(f"Error creating cloth simulation: {e}")
            return {"success": False, "message": f"Error creating cloth simulation: {e}"}

    @mcp.tool()
    def optimize_chaos_performance(
        optimization_level: str = "Medium",
        target_platform: str = "Windows",
        enable_gpu_acceleration: bool = True,
        max_particles: int = 10000
    ) -> Dict[str, Any]:
        """
        Optimize Chaos Physics performance settings.
        Compatible with HandleOptimizeChaosPerformance in C++.

        Args:
            optimization_level: Optimization level (Low, Medium, High, Extreme)
            target_platform: Target platform for optimization
            enable_gpu_acceleration: Enable GPU acceleration where available
            max_particles: Maximum number of particles for simulations
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "optimization_level": optimization_level,
                "target_platform": target_platform,
                "enable_gpu_acceleration": enable_gpu_acceleration,
                "max_particles": max_particles
            }

            response = unreal.send_command("optimize_chaos_performance", params)
            return response or {"success": True, "message": "Chaos performance optimized successfully"}

        except Exception as e:
            logger.error(f"Error optimizing chaos performance: {e}")
            return {"success": False, "message": f"Error optimizing chaos performance: {e}"}

    @mcp.tool()
    def create_rigid_body_simulation(
        simulation_name: str,
        mesh_path: str,
        simulation_path: str = "/Game/Physics/RigidBodies",
        mass: float = 1.0,
        linear_damping: float = 0.01,
        angular_damping: float = 0.01
    ) -> Dict[str, Any]:
        """
        Create a rigid body simulation.
        Compatible with HandleCreateRigidBodySimulation in C++.

        Args:
            simulation_name: Name of the rigid body simulation (required)
            mesh_path: Path to the source mesh (required)
            simulation_path: Content browser path for the simulation
            mass: Mass of the rigid body
            linear_damping: Linear damping value
            angular_damping: Angular damping value
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "simulation_name": simulation_name,
                "mesh_path": mesh_path,
                "simulation_path": simulation_path,
                "mass": mass,
                "linear_damping": linear_damping,
                "angular_damping": angular_damping
            }

            response = unreal.send_command("create_rigid_body_simulation", params)
            return response or {"success": True, "message": "Rigid body simulation created successfully"}

        except Exception as e:
            logger.error(f"Error creating rigid body simulation: {e}")
            return {"success": False, "message": f"Error creating rigid body simulation: {e}"}

    @mcp.tool()
    def analyze_chaos_performance(
        analysis_duration: float = 10.0,
        include_memory_stats: bool = True,
        include_timing_stats: bool = True,
        output_format: str = "JSON"
    ) -> Dict[str, Any]:
        """
        Analyze Chaos Physics performance metrics.
        Compatible with HandleAnalyzeChaosPerformance in C++.

        Args:
            analysis_duration: Duration of analysis in seconds
            include_memory_stats: Include memory usage statistics
            include_timing_stats: Include timing statistics
            output_format: Output format (JSON, CSV, XML)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "analysis_duration": analysis_duration,
                "include_memory_stats": include_memory_stats,
                "include_timing_stats": include_timing_stats,
                "output_format": output_format
            }

            response = unreal.send_command("analyze_chaos_performance", params)
            return response or {"success": True, "message": "Chaos performance analysis completed successfully"}

        except Exception as e:
            logger.error(f"Error analyzing chaos performance: {e}")
            return {"success": False, "message": f"Error analyzing chaos performance: {e}"}
