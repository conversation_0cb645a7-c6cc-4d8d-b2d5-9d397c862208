// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPChaosPhysicsCommands.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Engine/World.h"
#include "Engine/StaticMesh.h"
#include "GeometryCollection/GeometryCollectionEngineUtility.h"
#include "GeometryCollection/GeometryCollectionAlgo.h"
#include "Field/FieldSystemNodes.h"
#include "Chaos/ChaosGameplayEventDispatcher.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"
#include "PackageTools.h"
#include "FileHelpers.h"

// Initialize static constants
const TArray<FString> UnrealMCPChaosPhysicsCommands::SupportedFractureMethods = {
    TEXT("Voronoi"),
    TEXT("Uniform"),
    TEXT("Clustered"),
    TEXT("Radial"),
    TEXT("Planar"),
    TEXT("Brick"),
    TEXT("Custom")
};

const TArray<FString> UnrealMCPChaosPhysicsCommands::SupportedFieldTypes = {
    TEXT("RadialForce"),
    TEXT("DirectionalForce"),
    TEXT("NoiseField"),
    TEXT("UniformVector"),
    TEXT("RadialVector"),
    TEXT("RandomVector"),
    TEXT("CurlNoise"),
    TEXT("GravityField"),
    TEXT("MagneticField")
};

const TArray<FString> UnrealMCPChaosPhysicsCommands::SupportedFluidTypes = {
    TEXT("Water"),
    TEXT("Oil"),
    TEXT("Gas"),
    TEXT("Lava"),
    TEXT("Acid"),
    TEXT("Custom")
};

const TArray<FString> UnrealMCPChaosPhysicsCommands::SupportedOptimizationLevels = {
    TEXT("Low"),
    TEXT("Medium"),
    TEXT("High"),
    TEXT("Extreme")
};

const TArray<FString> UnrealMCPChaosPhysicsCommands::SupportedPlatforms = {
    TEXT("Windows"),
    TEXT("Mac"),
    TEXT("Linux"),
    TEXT("Android"),
    TEXT("iOS"),
    TEXT("PlayStation5"),
    TEXT("XboxSeriesX"),
    TEXT("Switch")
};

const TArray<FString> UnrealMCPChaosPhysicsCommands::SupportedOutputFormats = {
    TEXT("JSON"),
    TEXT("CSV"),
    TEXT("XML")
};

UnrealMCPChaosPhysicsCommands::UnrealMCPChaosPhysicsCommands()
{
}

UnrealMCPChaosPhysicsCommands::~UnrealMCPChaosPhysicsCommands()
{
}

FString UnrealMCPChaosPhysicsCommands::HandleCreateGeometryCollection(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString CollectionName;
    if (!JsonObject->TryGetStringField(TEXT("collection_name"), CollectionName) || CollectionName.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty collection_name parameter"));
    }

    FString CollectionPath = TEXT("/Game/Physics/GeometryCollections");
    JsonObject->TryGetStringField(TEXT("collection_path"), CollectionPath);

    FString SourceMeshes;
    JsonObject->TryGetStringField(TEXT("source_meshes"), SourceMeshes);

    bool bAutoCluster = true;
    JsonObject->TryGetBoolField(TEXT("auto_cluster"), bAutoCluster);

    // Validate parameters
    if (!ValidateChaosPath(CollectionPath))
    {
        return CreateJsonResponse(false, TEXT("Invalid collection path"));
    }

    // Create the geometry collection
    UGeometryCollection* NewCollection = CreateGeometryCollectionAsset(CollectionName, CollectionPath);
    if (!NewCollection)
    {
        return CreateJsonResponse(false, TEXT("Failed to create geometry collection asset"));
    }

    // Add source meshes if provided
    if (!SourceMeshes.IsEmpty())
    {
        TArray<FString> MeshPaths;
        SourceMeshes.ParseIntoArray(MeshPaths, TEXT(","), true);
        
        if (!AddMeshesToGeometryCollection(NewCollection, MeshPaths))
        {
            return CreateJsonResponse(false, TEXT("Failed to add meshes to geometry collection"));
        }
    }

    // Configure clustering
    if (bAutoCluster)
    {
        // Apply automatic clustering
        FGeometryCollectionClusteringUtility::ClusterAllChildren(NewCollection);
    }

    // Save the collection
    FString PackageName = ChaosPathToPackageName(CollectionPath + TEXT("/") + CollectionName);
    if (!SaveChaosAsset(NewCollection, PackageName))
    {
        return CreateJsonResponse(false, TEXT("Failed to save geometry collection asset"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("collection_path"), PackageName);
    ResponseData.Add(TEXT("collection_name"), CollectionName);
    ResponseData.Add(TEXT("auto_cluster"), bAutoCluster ? TEXT("true") : TEXT("false"));

    return CreateJsonResponse(true, TEXT("Geometry collection created successfully"), ResponseData);
}

FString UnrealMCPChaosPhysicsCommands::HandleFractureGeometryCollection(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString CollectionPath;
    if (!JsonObject->TryGetStringField(TEXT("collection_path"), CollectionPath) || CollectionPath.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty collection_path parameter"));
    }

    FString FractureMethod = TEXT("Voronoi");
    JsonObject->TryGetStringField(TEXT("fracture_method"), FractureMethod);

    int32 FractureCount = 10;
    JsonObject->TryGetNumberField(TEXT("fracture_count"), FractureCount);

    int32 RandomSeed = 42;
    JsonObject->TryGetNumberField(TEXT("random_seed"), RandomSeed);

    // Validate parameters
    if (!ValidateFractureMethod(FractureMethod))
    {
        return CreateJsonResponse(false, FString::Printf(TEXT("Unsupported fracture method: %s"), *FractureMethod));
    }

    // Load geometry collection
    UGeometryCollection* Collection = LoadGeometryCollectionFromPath(CollectionPath);
    if (!Collection)
    {
        return CreateJsonResponse(false, TEXT("Failed to load geometry collection"));
    }

    // Perform fracturing
    if (!FractureGeometryCollectionInternal(Collection, FractureMethod, FractureCount, RandomSeed))
    {
        return CreateJsonResponse(false, TEXT("Failed to fracture geometry collection"));
    }

    // Mark collection as modified
    Collection->MarkPackageDirty();

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("collection_path"), CollectionPath);
    ResponseData.Add(TEXT("fracture_method"), FractureMethod);
    ResponseData.Add(TEXT("fracture_count"), FString::FromInt(FractureCount));
    ResponseData.Add(TEXT("random_seed"), FString::FromInt(RandomSeed));

    return CreateJsonResponse(true, TEXT("Geometry collection fractured successfully"), ResponseData);
}

FString UnrealMCPChaosPhysicsCommands::HandleCreateChaosPhysicsField(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString FieldName;
    if (!JsonObject->TryGetStringField(TEXT("field_name"), FieldName) || FieldName.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty field_name parameter"));
    }

    FString FieldType;
    if (!JsonObject->TryGetStringField(TEXT("field_type"), FieldType) || FieldType.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty field_type parameter"));
    }

    FString FieldLocation = TEXT("0,0,0");
    JsonObject->TryGetStringField(TEXT("field_location"), FieldLocation);

    double FieldRadius = 500.0;
    JsonObject->TryGetNumberField(TEXT("field_radius"), FieldRadius);

    double FieldStrength = 1000.0;
    JsonObject->TryGetNumberField(TEXT("field_strength"), FieldStrength);

    // Validate parameters
    if (!ValidateFieldType(FieldType))
    {
        return CreateJsonResponse(false, FString::Printf(TEXT("Unsupported field type: %s"), *FieldType));
    }

    // Parse location
    TArray<FString> LocationComponents;
    FieldLocation.ParseIntoArray(LocationComponents, TEXT(","), true);
    FVector Location = FVector::ZeroVector;
    if (LocationComponents.Num() >= 3)
    {
        Location.X = FCString::Atof(*LocationComponents[0]);
        Location.Y = FCString::Atof(*LocationComponents[1]);
        Location.Z = FCString::Atof(*LocationComponents[2]);
    }

    // Create physics field actor
    AFieldSystemActor* FieldActor = CreatePhysicsFieldActor(FieldName, FieldType, Location);
    if (!FieldActor)
    {
        return CreateJsonResponse(false, TEXT("Failed to create physics field actor"));
    }

    // Setup field component
    UFieldSystemComponent* FieldComponent = SetupFieldComponent(FieldActor, FieldType, 
        static_cast<float>(FieldRadius), static_cast<float>(FieldStrength));
    if (!FieldComponent)
    {
        return CreateJsonResponse(false, TEXT("Failed to setup field component"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("field_name"), FieldName);
    ResponseData.Add(TEXT("field_type"), FieldType);
    ResponseData.Add(TEXT("field_location"), FieldLocation);
    ResponseData.Add(TEXT("field_radius"), FString::SanitizeFloat(FieldRadius));
    ResponseData.Add(TEXT("field_strength"), FString::SanitizeFloat(FieldStrength));

    return CreateJsonResponse(true, TEXT("Chaos physics field created successfully"), ResponseData);
}

// Helper function implementations
UGeometryCollection* UnrealMCPChaosPhysicsCommands::CreateGeometryCollectionAsset(
    const FString& CollectionName, const FString& PackagePath)
{
    FString PackageName = ChaosPathToPackageName(PackagePath + TEXT("/") + CollectionName);
    UPackage* Package = CreatePackage(*PackageName);
    if (!Package)
    {
        return nullptr;
    }

    UGeometryCollectionFactory* CollectionFactory = NewObject<UGeometryCollectionFactory>();
    UGeometryCollection* NewCollection = Cast<UGeometryCollection>(CollectionFactory->FactoryCreateNew(
        UGeometryCollection::StaticClass(), Package, FName(*CollectionName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (NewCollection)
    {
        FAssetRegistryModule::AssetCreated(NewCollection);
        Package->MarkPackageDirty();
    }

    return NewCollection;
}

bool UnrealMCPChaosPhysicsCommands::AddMeshesToGeometryCollection(UGeometryCollection* Collection,
    const TArray<FString>& MeshPaths)
{
    if (!Collection)
    {
        return false;
    }

    for (const FString& MeshPath : MeshPaths)
    {
        UStaticMesh* StaticMesh = LoadObject<UStaticMesh>(nullptr, *MeshPath);
        if (StaticMesh)
        {
            // Add mesh to geometry collection
            FGeometryCollectionEngineUtility::AppendStaticMesh(StaticMesh, nullptr,
                FTransform::Identity, Collection, false);
        }
    }

    return true;
}

bool UnrealMCPChaosPhysicsCommands::FractureGeometryCollectionInternal(UGeometryCollection* Collection,
    const FString& FractureMethod, int32 FractureCount, int32 RandomSeed)
{
    if (!Collection)
    {
        return false;
    }

    // Set random seed
    FMath::RandInit(RandomSeed);

    // Apply fracturing based on method
    if (FractureMethod == TEXT("Voronoi"))
    {
        // Apply Voronoi fracturing
        FGeometryCollectionAlgo::VoronoiFracture(Collection, FractureCount);
    }
    else if (FractureMethod == TEXT("Uniform"))
    {
        // Apply uniform fracturing
        FGeometryCollectionAlgo::UniformFracture(Collection, FractureCount);
    }
    else if (FractureMethod == TEXT("Radial"))
    {
        // Apply radial fracturing
        FGeometryCollectionAlgo::RadialFracture(Collection, FractureCount, FVector::ZeroVector);
    }
    else if (FractureMethod == TEXT("Planar"))
    {
        // Apply planar fracturing
        FGeometryCollectionAlgo::PlanarFracture(Collection, FractureCount);
    }

    return true;
}

AFieldSystemActor* UnrealMCPChaosPhysicsCommands::CreatePhysicsFieldActor(const FString& FieldName,
    const FString& FieldType, const FVector& Location)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return nullptr;
    }

    // Spawn field system actor
    AFieldSystemActor* FieldActor = World->SpawnActor<AFieldSystemActor>();
    if (FieldActor)
    {
        FieldActor->SetActorLocation(Location);
        FieldActor->SetActorLabel(FieldName);
    }

    return FieldActor;
}

UFieldSystemComponent* UnrealMCPChaosPhysicsCommands::SetupFieldComponent(AFieldSystemActor* FieldActor,
    const FString& FieldType, float Radius, float Strength)
{
    if (!FieldActor)
    {
        return nullptr;
    }

    UFieldSystemComponent* FieldComponent = FieldActor->GetFieldSystemComponent();
    if (!FieldComponent)
    {
        return nullptr;
    }

    // Configure field based on type
    if (FieldType == TEXT("RadialForce"))
    {
        ConfigureRadialForceField(FieldComponent, Radius, Strength);
    }
    else if (FieldType == TEXT("DirectionalForce"))
    {
        ConfigureDirectionalForceField(FieldComponent, FVector(0, 0, 1), Strength);
    }

    return FieldComponent;
}

bool UnrealMCPChaosPhysicsCommands::ConfigureRadialForceField(UFieldSystemComponent* FieldComponent,
    float Radius, float Strength)
{
    if (!FieldComponent)
    {
        return false;
    }

    // Create radial force field nodes
    URadialVector* RadialVector = NewObject<URadialVector>(FieldComponent);
    RadialVector->Magnitude = Strength;
    RadialVector->Position = FVector::ZeroVector;

    USphere* SphereField = NewObject<USphere>(FieldComponent);
    SphereField->Radius = Radius;
    SphereField->Center = FVector::ZeroVector;

    // Set up the field system
    FieldComponent->SetFieldSystem(RadialVector, SphereField);

    return true;
}

TSharedPtr<FJsonObject> UnrealMCPChaosPhysicsCommands::ParseJsonParams(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonParams);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return nullptr;
    }

    return JsonObject;
}

FString UnrealMCPChaosPhysicsCommands::CreateJsonResponse(bool bSuccess, const FString& Message,
    const TMap<FString, FString>& AdditionalData)
{
    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), bSuccess);
    ResponseObject->SetStringField(TEXT("message"), Message);

    if (AdditionalData.Num() > 0)
    {
        TSharedPtr<FJsonObject> DataObject = MakeShareable(new FJsonObject);
        for (const auto& Pair : AdditionalData)
        {
            DataObject->SetStringField(Pair.Key, Pair.Value);
        }
        ResponseObject->SetObjectField(TEXT("data"), DataObject);
    }

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

FString UnrealMCPChaosPhysicsCommands::ChaosPathToPackageName(const FString& ChaosPath)
{
    FString PackageName = ChaosPath;
    if (!PackageName.StartsWith(TEXT("/Game/")))
    {
        PackageName = TEXT("/Game/") + PackageName;
    }
    return PackageName;
}

bool UnrealMCPChaosPhysicsCommands::ValidateChaosPath(const FString& ChaosPath)
{
    return !ChaosPath.IsEmpty() && (ChaosPath.StartsWith(TEXT("/Game/")) || ChaosPath.StartsWith(TEXT("/Engine/")));
}

bool UnrealMCPChaosPhysicsCommands::ValidateFractureMethod(const FString& FractureMethod)
{
    return SupportedFractureMethods.Contains(FractureMethod);
}

bool UnrealMCPChaosPhysicsCommands::ValidateFieldType(const FString& FieldType)
{
    return SupportedFieldTypes.Contains(FieldType);
}
