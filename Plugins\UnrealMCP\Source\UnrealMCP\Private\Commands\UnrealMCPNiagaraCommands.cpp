// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPNiagaraCommands.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Engine/World.h"
#include "Engine/StaticMesh.h"
#include "Engine/Texture2D.h"
#include "NiagaraEmitterEditorData.h"
#include "NiagaraNodeOutput.h"
#include "NiagaraNodeFunctionCall.h"
#include "NiagaraGraph.h"
#include "NiagaraScriptSource.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"
#include "PackageTools.h"
#include "FileHelpers.h"

// Initialize static constants
const TArray<FString> UnrealMCPNiagaraCommands::SupportedEmitterTypes = {
    TEXT("Sprite"),
    TEXT("Mesh"),
    TEXT("Ribbon"),
    TEXT("Light"),
    TEXT("Beam"),
    TEXT("GPU"),
    TEXT("CPU")
};

const TArray<FString> UnrealMCPNiagaraCommands::SupportedModuleTypes = {
    TEXT("SpawnRate"),
    TEXT("SpawnBurst"),
    TEXT("InitialVelocity"),
    TEXT("InitialColor"),
    TEXT("InitialSize"),
    TEXT("InitialLifetime"),
    TEXT("InitialLocation"),
    TEXT("InitialRotation"),
    TEXT("UpdateAge"),
    TEXT("UpdateColor"),
    TEXT("UpdateSize"),
    TEXT("UpdateVelocity"),
    TEXT("Gravity"),
    TEXT("Drag"),
    TEXT("CurlNoise"),
    TEXT("Vortex"),
    TEXT("PointAttractor"),
    TEXT("CollisionPlane"),
    TEXT("KillParticles"),
    TEXT("SpriteRenderer"),
    TEXT("MeshRenderer"),
    TEXT("RibbonRenderer"),
    TEXT("LightRenderer")
};

const TArray<FString> UnrealMCPNiagaraCommands::SupportedParameterTypes = {
    TEXT("Float"),
    TEXT("Vector2"),
    TEXT("Vector3"),
    TEXT("Vector4"),
    TEXT("Color"),
    TEXT("Bool"),
    TEXT("Int"),
    TEXT("Texture2D"),
    TEXT("StaticMesh"),
    TEXT("Material")
};

const TArray<FString> UnrealMCPNiagaraCommands::SupportedModuleGroups = {
    TEXT("SystemSpawn"),
    TEXT("SystemUpdate"),
    TEXT("EmitterSpawn"),
    TEXT("EmitterUpdate"),
    TEXT("ParticleSpawn"),
    TEXT("ParticleUpdate"),
    TEXT("EventHandler"),
    TEXT("Render")
};

const TArray<FString> UnrealMCPNiagaraCommands::SupportedOptimizationLevels = {
    TEXT("Low"),
    TEXT("Medium"),
    TEXT("High"),
    TEXT("Extreme")
};

const TArray<FString> UnrealMCPNiagaraCommands::SupportedSimulationTargets = {
    TEXT("CPUSim"),
    TEXT("GPUComputeSim")
};

UnrealMCPNiagaraCommands::UnrealMCPNiagaraCommands()
{
}

UnrealMCPNiagaraCommands::~UnrealMCPNiagaraCommands()
{
}

FString UnrealMCPNiagaraCommands::HandleCreateNiagaraSystem(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString SystemName;
    if (!JsonObject->TryGetStringField(TEXT("system_name"), SystemName) || SystemName.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty system_name parameter"));
    }

    FString SystemPath = TEXT("/Game/VFX/Systems");
    JsonObject->TryGetStringField(TEXT("system_path"), SystemPath);

    FString TemplateType = TEXT("Empty");
    JsonObject->TryGetStringField(TEXT("template_type"), TemplateType);

    bool bAutoActivate = true;
    JsonObject->TryGetBoolField(TEXT("auto_activate"), bAutoActivate);

    // Validate parameters
    if (!ValidateNiagaraPath(SystemPath))
    {
        return CreateJsonResponse(false, TEXT("Invalid system path"));
    }

    // Create the Niagara system
    UNiagaraSystem* NewSystem = CreateNiagaraSystemAsset(SystemName, SystemPath);
    if (!NewSystem)
    {
        return CreateJsonResponse(false, TEXT("Failed to create Niagara system asset"));
    }

    // Configure system properties
    NewSystem->SetAutoActivate(bAutoActivate);

    // Apply template if specified
    if (TemplateType != TEXT("Empty"))
    {
        // Apply template-specific settings
        if (TemplateType == TEXT("Fountain"))
        {
            // Add fountain-specific emitters and modules
        }
        else if (TemplateType == TEXT("SimpleSprite"))
        {
            // Add simple sprite emitter
        }
    }

    // Save the system
    FString PackageName = NiagaraPathToPackageName(SystemPath + TEXT("/") + SystemName);
    if (!SaveNiagaraAsset(NewSystem, PackageName))
    {
        return CreateJsonResponse(false, TEXT("Failed to save Niagara system asset"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("system_path"), PackageName);
    ResponseData.Add(TEXT("system_name"), SystemName);
    ResponseData.Add(TEXT("template_type"), TemplateType);

    return CreateJsonResponse(true, TEXT("Niagara system created successfully"), ResponseData);
}

FString UnrealMCPNiagaraCommands::HandleCreateNiagaraEmitter(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString EmitterName;
    if (!JsonObject->TryGetStringField(TEXT("emitter_name"), EmitterName) || EmitterName.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty emitter_name parameter"));
    }

    FString EmitterPath = TEXT("/Game/VFX/Emitters");
    JsonObject->TryGetStringField(TEXT("emitter_path"), EmitterPath);

    FString EmitterType = TEXT("Sprite");
    JsonObject->TryGetStringField(TEXT("emitter_type"), EmitterType);

    FString ParentEmitter;
    JsonObject->TryGetStringField(TEXT("parent_emitter"), ParentEmitter);

    // Validate parameters
    if (!ValidateNiagaraPath(EmitterPath))
    {
        return CreateJsonResponse(false, TEXT("Invalid emitter path"));
    }

    if (!ValidateEmitterType(EmitterType))
    {
        return CreateJsonResponse(false, FString::Printf(TEXT("Unsupported emitter type: %s"), *EmitterType));
    }

    // Create the emitter
    UNiagaraEmitter* NewEmitter = CreateNiagaraEmitterAsset(EmitterName, EmitterPath, EmitterType);
    if (!NewEmitter)
    {
        return CreateJsonResponse(false, TEXT("Failed to create Niagara emitter asset"));
    }

    // Setup renderer based on emitter type
    if (EmitterType == TEXT("Sprite"))
    {
        SetupSpriteRenderer(NewEmitter);
    }
    else if (EmitterType == TEXT("Mesh"))
    {
        SetupMeshRenderer(NewEmitter, nullptr);
    }
    else if (EmitterType == TEXT("Ribbon"))
    {
        SetupRibbonRenderer(NewEmitter);
    }
    else if (EmitterType == TEXT("Light"))
    {
        SetupLightRenderer(NewEmitter);
    }

    // Add basic modules
    AddSpawnRateModule(NewEmitter, 10.0f);
    AddInitialVelocityModule(NewEmitter, FVector(0, 0, 100));
    AddInitialColorModule(NewEmitter, FLinearColor::White);
    AddInitialSizeModule(NewEmitter, FVector2D(50, 50));
    AddLifetimeModule(NewEmitter, 5.0f);

    // Save the emitter
    FString PackageName = NiagaraPathToPackageName(EmitterPath + TEXT("/") + EmitterName);
    if (!SaveNiagaraAsset(NewEmitter, PackageName))
    {
        return CreateJsonResponse(false, TEXT("Failed to save Niagara emitter asset"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("emitter_path"), PackageName);
    ResponseData.Add(TEXT("emitter_name"), EmitterName);
    ResponseData.Add(TEXT("emitter_type"), EmitterType);

    return CreateJsonResponse(true, TEXT("Niagara emitter created successfully"), ResponseData);
}

FString UnrealMCPNiagaraCommands::HandleAddEmitterToSystem(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString SystemPath;
    if (!JsonObject->TryGetStringField(TEXT("system_path"), SystemPath) || SystemPath.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty system_path parameter"));
    }

    FString EmitterPath;
    if (!JsonObject->TryGetStringField(TEXT("emitter_path"), EmitterPath) || EmitterPath.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Missing or empty emitter_path parameter"));
    }

    FString EmitterName;
    JsonObject->TryGetStringField(TEXT("emitter_name"), EmitterName);

    bool bEnabled = true;
    JsonObject->TryGetBoolField(TEXT("enabled"), bEnabled);

    // Load system and emitter
    UNiagaraSystem* System = LoadNiagaraSystemFromPath(SystemPath);
    if (!System)
    {
        return CreateJsonResponse(false, TEXT("Failed to load Niagara system"));
    }

    UNiagaraEmitter* Emitter = LoadNiagaraEmitterFromPath(EmitterPath);
    if (!Emitter)
    {
        return CreateJsonResponse(false, TEXT("Failed to load Niagara emitter"));
    }

    // Add emitter to system
    if (!AddEmitterToNiagaraSystem(System, Emitter, EmitterName))
    {
        return CreateJsonResponse(false, TEXT("Failed to add emitter to system"));
    }

    // Mark system as modified
    System->MarkPackageDirty();

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("system_path"), SystemPath);
    ResponseData.Add(TEXT("emitter_path"), EmitterPath);
    ResponseData.Add(TEXT("emitter_name"), EmitterName.IsEmpty() ? Emitter->GetName() : EmitterName);

    return CreateJsonResponse(true, TEXT("Emitter added to system successfully"), ResponseData);
}

// Helper function implementations
UNiagaraSystem* UnrealMCPNiagaraCommands::CreateNiagaraSystemAsset(const FString& SystemName, const FString& PackagePath)
{
    FString PackageName = NiagaraPathToPackageName(PackagePath + TEXT("/") + SystemName);
    UPackage* Package = CreatePackage(*PackageName);
    if (!Package)
    {
        return nullptr;
    }

    UNiagaraSystemFactoryNew* SystemFactory = NewObject<UNiagaraSystemFactoryNew>();
    UNiagaraSystem* NewSystem = Cast<UNiagaraSystem>(SystemFactory->FactoryCreateNew(
        UNiagaraSystem::StaticClass(), Package, FName(*SystemName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (NewSystem)
    {
        FAssetRegistryModule::AssetCreated(NewSystem);
        Package->MarkPackageDirty();
    }

    return NewSystem;
}

UNiagaraEmitter* UnrealMCPNiagaraCommands::CreateNiagaraEmitterAsset(
    const FString& EmitterName, const FString& PackagePath, const FString& EmitterType)
{
    FString PackageName = NiagaraPathToPackageName(PackagePath + TEXT("/") + EmitterName);
    UPackage* Package = CreatePackage(*PackageName);
    if (!Package)
    {
        return nullptr;
    }

    UNiagaraEmitterFactoryNew* EmitterFactory = NewObject<UNiagaraEmitterFactoryNew>();
    UNiagaraEmitter* NewEmitter = Cast<UNiagaraEmitter>(EmitterFactory->FactoryCreateNew(
        UNiagaraEmitter::StaticClass(), Package, FName(*EmitterName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (NewEmitter)
    {
        FAssetRegistryModule::AssetCreated(NewEmitter);
        Package->MarkPackageDirty();
    }

    return NewEmitter;
}

bool UnrealMCPNiagaraCommands::AddEmitterToNiagaraSystem(UNiagaraSystem* System, UNiagaraEmitter* Emitter, const FString& EmitterName)
{
    if (!System || !Emitter)
    {
        return false;
    }

    // Create emitter handle
    FNiagaraEmitterHandle EmitterHandle;
    EmitterHandle.SetSource(Emitter);
    EmitterHandle.SetName(FName(*EmitterName.IsEmpty() ? Emitter->GetName() : EmitterName));
    EmitterHandle.SetIsEnabled(true);

    // Add to system
    System->AddEmitterHandle(EmitterHandle);
    System->RequestCompile(false);

    return true;
}

UNiagaraSpriteRendererProperties* UnrealMCPNiagaraCommands::SetupSpriteRenderer(UNiagaraEmitter* Emitter)
{
    if (!Emitter)
    {
        return nullptr;
    }

    UNiagaraSpriteRendererProperties* SpriteRenderer = NewObject<UNiagaraSpriteRendererProperties>(Emitter);
    if (SpriteRenderer)
    {
        // Configure sprite renderer properties
        SpriteRenderer->Material = nullptr; // Use default material
        SpriteRenderer->Alignment = ENiagaraSpriteAlignment::Unaligned;
        SpriteRenderer->FacingMode = ENiagaraSpriteFacingMode::FaceCamera;
        SpriteRenderer->PivotInUVSpace = FVector2D(0.5f, 0.5f);
        SpriteRenderer->SortMode = ENiagaraSortMode::ViewDistance;

        // Add to emitter
        Emitter->AddRenderer(SpriteRenderer);
    }

    return SpriteRenderer;
}

UNiagaraMeshRendererProperties* UnrealMCPNiagaraCommands::SetupMeshRenderer(UNiagaraEmitter* Emitter, UStaticMesh* Mesh)
{
    if (!Emitter)
    {
        return nullptr;
    }

    UNiagaraMeshRendererProperties* MeshRenderer = NewObject<UNiagaraMeshRendererProperties>(Emitter);
    if (MeshRenderer)
    {
        // Configure mesh renderer properties
        if (Mesh)
        {
            MeshRenderer->ParticleMesh = Mesh;
        }
        MeshRenderer->SortMode = ENiagaraSortMode::ViewDistance;
        MeshRenderer->bSortOnlyWhenTranslucent = true;

        // Add to emitter
        Emitter->AddRenderer(MeshRenderer);
    }

    return MeshRenderer;
}

UNiagaraRibbonRendererProperties* UnrealMCPNiagaraCommands::SetupRibbonRenderer(UNiagaraEmitter* Emitter)
{
    if (!Emitter)
    {
        return nullptr;
    }

    UNiagaraRibbonRendererProperties* RibbonRenderer = NewObject<UNiagaraRibbonRendererProperties>(Emitter);
    if (RibbonRenderer)
    {
        // Configure ribbon renderer properties
        RibbonRenderer->Material = nullptr; // Use default material
        RibbonRenderer->FacingMode = ENiagaraRibbonFacingMode::Screen;
        RibbonRenderer->UV0TilingDistance = 0.0f;
        RibbonRenderer->UV0Scale = FVector2D(1.0f, 1.0f);
        RibbonRenderer->UV0Offset = FVector2D(0.0f, 0.0f);

        // Add to emitter
        Emitter->AddRenderer(RibbonRenderer);
    }

    return RibbonRenderer;
}

UNiagaraLightRendererProperties* UnrealMCPNiagaraCommands::SetupLightRenderer(UNiagaraEmitter* Emitter)
{
    if (!Emitter)
    {
        return nullptr;
    }

    UNiagaraLightRendererProperties* LightRenderer = NewObject<UNiagaraLightRendererProperties>(Emitter);
    if (LightRenderer)
    {
        // Configure light renderer properties
        LightRenderer->bUseInverseSquaredFalloff = true;
        LightRenderer->bAffectsTranslucency = true;
        LightRenderer->bAlphaScalesBrightness = true;
        LightRenderer->RadiusScale = 1.0f;
        LightRenderer->DefaultLightScale = FVector(1.0f, 1.0f, 1.0f);

        // Add to emitter
        Emitter->AddRenderer(LightRenderer);
    }

    return LightRenderer;
}

TSharedPtr<FJsonObject> UnrealMCPNiagaraCommands::ParseJsonParams(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonParams);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return nullptr;
    }

    return JsonObject;
}

FString UnrealMCPNiagaraCommands::CreateJsonResponse(bool bSuccess, const FString& Message,
    const TMap<FString, FString>& AdditionalData)
{
    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), bSuccess);
    ResponseObject->SetStringField(TEXT("message"), Message);

    if (AdditionalData.Num() > 0)
    {
        TSharedPtr<FJsonObject> DataObject = MakeShareable(new FJsonObject);
        for (const auto& Pair : AdditionalData)
        {
            DataObject->SetStringField(Pair.Key, Pair.Value);
        }
        ResponseObject->SetObjectField(TEXT("data"), DataObject);
    }

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

FString UnrealMCPNiagaraCommands::NiagaraPathToPackageName(const FString& NiagaraPath)
{
    FString PackageName = NiagaraPath;
    if (!PackageName.StartsWith(TEXT("/Game/")))
    {
        PackageName = TEXT("/Game/") + PackageName;
    }
    return PackageName;
}

bool UnrealMCPNiagaraCommands::ValidateNiagaraPath(const FString& NiagaraPath)
{
    return !NiagaraPath.IsEmpty() && (NiagaraPath.StartsWith(TEXT("/Game/")) || NiagaraPath.StartsWith(TEXT("/Engine/")));
}

bool UnrealMCPNiagaraCommands::ValidateEmitterType(const FString& EmitterType)
{
    return SupportedEmitterTypes.Contains(EmitterType);
}
