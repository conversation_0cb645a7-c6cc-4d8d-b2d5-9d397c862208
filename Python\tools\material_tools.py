"""
Material Tools for Unreal Engine MCP Server

This module provides comprehensive material system tools that are 100% compatible with the 
C++ implementations in UnrealMCPMaterialCommands.cpp. Based on UE 5.6 official documentation.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP

# Set up logging
logger = logging.getLogger("UnrealMCP")

def register_material_tools(mcp: FastMCP):
    """Register Material tools with the MCP server - 100% compatible with C++ implementations."""

    @mcp.tool()
    def create_material(
        material_name: str,
        material_path: str = "/Game/Materials",
        shading_model: str = "MSM_DefaultLit",
        blend_mode: str = "BLEND_Opaque",
        two_sided: bool = False
    ) -> Dict[str, Any]:
        """
        Create a new Material asset using the Material Editor.
        Compatible with HandleCreateMaterial in C++.
        
        Args:
            material_name: Name of the material to create (required)
            material_path: Content browser path for the material (default: /Game/Materials)
            shading_model: Shading model (MSM_DefaultLit, MSM_Unlit, MSM_Subsurface, etc.)
            blend_mode: Blend mode (BLEND_Opaque, BLEND_Translucent, BLEND_Masked, etc.)
            two_sided: Enable two-sided rendering
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "material_name": material_name,
                "material_path": material_path,
                "shading_model": shading_model,
                "blend_mode": blend_mode,
                "two_sided": two_sided
            }

            response = unreal.send_command("create_material", params)
            return response or {"success": True, "message": "Material created successfully"}

        except Exception as e:
            logger.error(f"Error creating material: {e}")
            return {"success": False, "message": f"Error creating material: {e}"}

    @mcp.tool()
    def create_material_instance(
        instance_name: str,
        parent_material_path: str,
        instance_path: str = "/Game/Materials/Instances",
        parameters_json: str = ""
    ) -> Dict[str, Any]:
        """
        Create a Material Instance from a parent material.
        Compatible with HandleCreateMaterialInstance in C++.
        
        Args:
            instance_name: Name of the material instance (required)
            parent_material_path: Path to the parent material (required)
            instance_path: Content browser path for the instance
            parameters_json: Initial parameter values as JSON string
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "instance_name": instance_name,
                "parent_material_path": parent_material_path,
                "instance_path": instance_path
            }

            # Add parameters if provided
            if parameters_json and parameters_json.strip():
                try:
                    params["parameters"] = json.loads(parameters_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for parameters"}

            response = unreal.send_command("create_material_instance", params)
            return response or {"success": True, "message": "Material instance created successfully"}

        except Exception as e:
            logger.error(f"Error creating material instance: {e}")
            return {"success": False, "message": f"Error creating material instance: {e}"}

    @mcp.tool()
    def add_material_expression(
        material_path: str,
        expression_type: str,
        expression_name: str = "",
        position_x: float = 0.0,
        position_y: float = 0.0,
        properties_json: str = ""
    ) -> Dict[str, Any]:
        """
        Add a Material Expression node to a material graph.
        Compatible with HandleAddMaterialExpression in C++.
        
        Args:
            material_path: Path to the material asset (required)
            expression_type: Type of expression (TextureSample, Constant, Multiply, etc.)
            expression_name: Optional name for the expression
            position_x: X position in the material graph
            position_y: Y position in the material graph
            properties_json: Expression properties as JSON string
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "material_path": material_path,
                "expression_type": expression_type,
                "expression_name": expression_name,
                "position_x": position_x,
                "position_y": position_y
            }

            # Add properties if provided
            if properties_json and properties_json.strip():
                try:
                    params["properties"] = json.loads(properties_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for properties"}

            response = unreal.send_command("add_material_expression", params)
            return response or {"success": True, "message": "Material expression added successfully"}

        except Exception as e:
            logger.error(f"Error adding material expression: {e}")
            return {"success": False, "message": f"Error adding material expression: {e}"}

    @mcp.tool()
    def connect_material_expressions(
        material_path: str,
        output_expression: str,
        output_index: int,
        input_expression: str,
        input_name: str
    ) -> Dict[str, Any]:
        """
        Connect two Material Expression nodes in the material graph.
        Compatible with HandleConnectMaterialExpressions in C++.
        
        Args:
            material_path: Path to the material asset (required)
            output_expression: Name/ID of the output expression (required)
            output_index: Output index of the source expression
            input_expression: Name/ID of the input expression (required)
            input_name: Name of the input pin (BaseColor, Normal, etc.)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "material_path": material_path,
                "output_expression": output_expression,
                "output_index": output_index,
                "input_expression": input_expression,
                "input_name": input_name
            }

            response = unreal.send_command("connect_material_expressions", params)
            return response or {"success": True, "message": "Material expressions connected successfully"}

        except Exception as e:
            logger.error(f"Error connecting material expressions: {e}")
            return {"success": False, "message": f"Error connecting material expressions: {e}"}

    @mcp.tool()
    def set_material_parameter(
        material_instance_path: str,
        parameter_name: str,
        parameter_type: str,
        parameter_value: str
    ) -> Dict[str, Any]:
        """
        Set a parameter value in a Material Instance.
        Compatible with HandleSetMaterialParameter in C++.
        
        Args:
            material_instance_path: Path to the material instance (required)
            parameter_name: Name of the parameter (required)
            parameter_type: Type of parameter (Scalar, Vector, Texture, StaticSwitch)
            parameter_value: Value as string (will be parsed based on type)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "material_instance_path": material_instance_path,
                "parameter_name": parameter_name,
                "parameter_type": parameter_type,
                "parameter_value": parameter_value
            }

            response = unreal.send_command("set_material_parameter", params)
            return response or {"success": True, "message": "Material parameter set successfully"}

        except Exception as e:
            logger.error(f"Error setting material parameter: {e}")
            return {"success": False, "message": f"Error setting material parameter: {e}"}

    @mcp.tool()
    def compile_material(
        material_path: str,
        force_recompile: bool = False
    ) -> Dict[str, Any]:
        """
        Compile a material and generate shaders.
        Compatible with HandleCompileMaterial in C++.
        
        Args:
            material_path: Path to the material asset (required)
            force_recompile: Force recompilation even if not needed
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "material_path": material_path,
                "force_recompile": force_recompile
            }

            response = unreal.send_command("compile_material", params)
            return response or {"success": True, "message": "Material compiled successfully"}

        except Exception as e:
            logger.error(f"Error compiling material: {e}")
            return {"success": False, "message": f"Error compiling material: {e}"}

    @mcp.tool()
    def configure_material_for_hardware_tier(
        material_path: str,
        hardware_tier: str,
        optimization_settings_json: str = ""
    ) -> Dict[str, Any]:
        """
        Configure material settings for specific hardware tiers.
        Compatible with HandleConfigureMaterialForHardwareTier in C++.
        
        Args:
            material_path: Path to the material asset (required)
            hardware_tier: Hardware tier (HighEnd, MidRange, LowEnd, Mobile)
            optimization_settings_json: Tier-specific optimization settings as JSON
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "material_path": material_path,
                "hardware_tier": hardware_tier
            }

            # Add optimization settings if provided
            if optimization_settings_json and optimization_settings_json.strip():
                try:
                    params["optimization_settings"] = json.loads(optimization_settings_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for optimization_settings"}

            response = unreal.send_command("configure_material_for_hardware_tier", params)
            return response or {"success": True, "message": "Material configured for hardware tier successfully"}

        except Exception as e:
            logger.error(f"Error configuring material for hardware tier: {e}")
            return {"success": False, "message": f"Error configuring material for hardware tier: {e}"}

    @mcp.tool()
    def create_procedural_material(
        material_name: str,
        material_path: str = "/Game/Materials/Procedural",
        generation_rules_json: str = "",
        seed: int = 12345
    ) -> Dict[str, Any]:
        """
        Create a procedural material with generation rules.
        Compatible with HandleCreateProceduralMaterial in C++.

        Args:
            material_name: Name of the procedural material (required)
            material_path: Content browser path for the material
            generation_rules_json: Procedural generation rules as JSON string
            seed: Random seed for procedural generation
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "material_name": material_name,
                "material_path": material_path,
                "seed": seed
            }

            # Add generation rules if provided
            if generation_rules_json and generation_rules_json.strip():
                try:
                    params["generation_rules"] = json.loads(generation_rules_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for generation_rules"}

            response = unreal.send_command("create_procedural_material", params)
            return response or {"success": True, "message": "Procedural material created successfully"}

        except Exception as e:
            logger.error(f"Error creating procedural material: {e}")
            return {"success": False, "message": f"Error creating procedural material: {e}"}

    @mcp.tool()
    def create_material_function(
        function_name: str,
        function_path: str = "/Game/Materials/Functions",
        function_description: str = "",
        input_parameters_json: str = "",
        output_parameters_json: str = ""
    ) -> Dict[str, Any]:
        """
        Create a Material Function for reusable material logic.
        Compatible with HandleCreateMaterialFunction in C++.

        Args:
            function_name: Name of the material function (required)
            function_path: Content browser path for the function
            function_description: Description of the function's purpose
            input_parameters_json: Input parameters definition as JSON
            output_parameters_json: Output parameters definition as JSON
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "function_name": function_name,
                "function_path": function_path,
                "function_description": function_description
            }

            # Add input parameters if provided
            if input_parameters_json and input_parameters_json.strip():
                try:
                    params["input_parameters"] = json.loads(input_parameters_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for input_parameters"}

            # Add output parameters if provided
            if output_parameters_json and output_parameters_json.strip():
                try:
                    params["output_parameters"] = json.loads(output_parameters_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for output_parameters"}

            response = unreal.send_command("create_material_function", params)
            return response or {"success": True, "message": "Material function created successfully"}

        except Exception as e:
            logger.error(f"Error creating material function: {e}")
            return {"success": False, "message": f"Error creating material function: {e}"}

    @mcp.tool()
    def create_layered_material(
        material_name: str,
        material_path: str = "/Game/Materials/Layered",
        layer_functions_json: str = "",
        blend_modes_json: str = ""
    ) -> Dict[str, Any]:
        """
        Create a layered material using Material Layers system.
        Compatible with HandleCreateLayeredMaterial in C++.

        Args:
            material_name: Name of the layered material (required)
            material_path: Content browser path for the material
            layer_functions_json: Array of layer function paths as JSON
            blend_modes_json: Blend modes for each layer as JSON
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "material_name": material_name,
                "material_path": material_path
            }

            # Add layer functions if provided
            if layer_functions_json and layer_functions_json.strip():
                try:
                    params["layer_functions"] = json.loads(layer_functions_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for layer_functions"}

            # Add blend modes if provided
            if blend_modes_json and blend_modes_json.strip():
                try:
                    params["blend_modes"] = json.loads(blend_modes_json)
                except json.JSONDecodeError:
                    return {"success": False, "message": "Invalid JSON format for blend_modes"}

            response = unreal.send_command("create_layered_material", params)
            return response or {"success": True, "message": "Layered material created successfully"}

        except Exception as e:
            logger.error(f"Error creating layered material: {e}")
            return {"success": False, "message": f"Error creating layered material: {e}"}

    @mcp.tool()
    def analyze_material_performance(
        material_path: str,
        target_platform: str = "Windows",
        analysis_depth: str = "Full"
    ) -> Dict[str, Any]:
        """
        Analyze material performance and shader complexity.
        Compatible with HandleAnalyzeMaterialPerformance in C++.

        Args:
            material_path: Path to the material asset (required)
            target_platform: Target platform (Windows, Android, iOS, etc.)
            analysis_depth: Analysis depth (Quick, Standard, Full)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "material_path": material_path,
                "target_platform": target_platform,
                "analysis_depth": analysis_depth
            }

            response = unreal.send_command("analyze_material_performance", params)
            return response or {"success": True, "message": "Material performance analysis completed"}

        except Exception as e:
            logger.error(f"Error analyzing material performance: {e}")
            return {"success": False, "message": f"Error analyzing material performance: {e}"}
