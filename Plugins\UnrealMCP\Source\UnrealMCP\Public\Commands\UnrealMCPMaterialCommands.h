// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Materials/Material.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialFunction.h"
#include "Materials/MaterialExpression.h"
#include "Materials/MaterialExpressionTextureSample.h"
#include "Materials/MaterialExpressionConstant.h"
#include "Materials/MaterialExpressionMultiply.h"
#include "Materials/MaterialExpressionAdd.h"
#include "Materials/MaterialParameterCollection.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "UObject/SavePackage.h"
#include "Misc/PackageName.h"
#include "Engine/Texture2D.h"
#include "EditorAssetLibrary.h"
#include "MaterialEditingLibrary.h"
#include "Kismet/KismetMaterialLibrary.h"

/**
 * UnrealMCPMaterialCommands
 * 
 * Handles all Material System related commands for the MCP server.
 * Provides comprehensive material creation, editing, and optimization tools
 * compatible with Unreal Engine 5.6 Material Editor and Shader system.
 * 
 * Features:
 * - Material and Material Instance creation
 * - Material Expression node management
 * - Procedural material generation
 * - Material Functions and Layered Materials
 * - Hardware-specific optimization
 * - Performance analysis and shader compilation
 * - Real-time parameter manipulation
 * - Cross-platform material configuration
 */
class UNREALMCP_API UnrealMCPMaterialCommands
{
public:
    UnrealMCPMaterialCommands();
    ~UnrealMCPMaterialCommands();

    // Core Material Creation and Management
    static FString HandleCreateMaterial(const FString& JsonParams);
    static FString HandleCreateMaterialInstance(const FString& JsonParams);
    static FString HandleAddMaterialExpression(const FString& JsonParams);
    static FString HandleConnectMaterialExpressions(const FString& JsonParams);
    static FString HandleSetMaterialParameter(const FString& JsonParams);
    static FString HandleCompileMaterial(const FString& JsonParams);

    // Advanced Material Features
    static FString HandleConfigureMaterialForHardwareTier(const FString& JsonParams);
    static FString HandleCreateProceduralMaterial(const FString& JsonParams);
    static FString HandleCreateMaterialFunction(const FString& JsonParams);
    static FString HandleCreateLayeredMaterial(const FString& JsonParams);
    static FString HandleAnalyzeMaterialPerformance(const FString& JsonParams);

    // Material Optimization and Analysis
    static FString HandleOptimizeMaterialForPlatform(const FString& JsonParams);
    static FString HandleGenerateShaderVariants(const FString& JsonParams);
    static FString HandleValidateMaterialComplexity(const FString& JsonParams);
    static FString HandleExportMaterialShaderCode(const FString& JsonParams);

private:
    // Helper Functions for Material Creation
    static UMaterial* CreateMaterialAsset(const FString& MaterialName, const FString& PackagePath);
    static UMaterialInstanceConstant* CreateMaterialInstanceAsset(const FString& InstanceName, 
        const FString& PackagePath, UMaterial* ParentMaterial);
    
    // Helper Functions for Material Expressions
    static UMaterialExpression* CreateMaterialExpression(UMaterial* Material, 
        const FString& ExpressionType, const FVector2D& Position);
    static bool ConnectMaterialExpressions(UMaterial* Material, 
        UMaterialExpression* OutputExpression, int32 OutputIndex,
        UMaterialExpression* InputExpression, const FString& InputName);
    
    // Helper Functions for Material Parameters
    static bool SetScalarParameter(UMaterialInstanceConstant* MaterialInstance, 
        const FString& ParameterName, float Value);
    static bool SetVectorParameter(UMaterialInstanceConstant* MaterialInstance, 
        const FString& ParameterName, const FLinearColor& Value);
    static bool SetTextureParameter(UMaterialInstanceConstant* MaterialInstance, 
        const FString& ParameterName, UTexture* Texture);
    static bool SetStaticSwitchParameter(UMaterialInstanceConstant* MaterialInstance, 
        const FString& ParameterName, bool Value);
    
    // Helper Functions for Material Compilation
    static bool CompileMaterialWithShaderPlatforms(UMaterial* Material, 
        const TArray<FName>& ShaderPlatforms);
    static FString GetMaterialCompilationErrors(UMaterial* Material);
    
    // Helper Functions for Hardware Optimization
    static void ConfigureMaterialForHighEnd(UMaterial* Material);
    static void ConfigureMaterialForMidRange(UMaterial* Material);
    static void ConfigureMaterialForLowEnd(UMaterial* Material);
    static void ConfigureMaterialForMobile(UMaterial* Material);
    
    // Helper Functions for Procedural Generation
    static UMaterial* GenerateProceduralMaterial(const FString& MaterialName, 
        const FString& PackagePath, const TMap<FString, FString>& GenerationRules, int32 Seed);
    static void AddProceduralNoiseNodes(UMaterial* Material, const TMap<FString, FString>& Rules);
    static void AddProceduralColorNodes(UMaterial* Material, const TMap<FString, FString>& Rules);
    
    // Helper Functions for Material Functions
    static UMaterialFunction* CreateMaterialFunctionAsset(const FString& FunctionName, 
        const FString& PackagePath);
    static void SetupMaterialFunctionInputs(UMaterialFunction* MaterialFunction, 
        const TArray<FString>& InputParameters);
    static void SetupMaterialFunctionOutputs(UMaterialFunction* MaterialFunction, 
        const TArray<FString>& OutputParameters);
    
    // Helper Functions for Layered Materials
    static UMaterial* CreateLayeredMaterialAsset(const FString& MaterialName, 
        const FString& PackagePath, const TArray<FString>& LayerFunctions);
    static void SetupMaterialLayers(UMaterial* Material, const TArray<FString>& LayerFunctions, 
        const TArray<FString>& BlendModes);
    
    // Helper Functions for Performance Analysis
    static TMap<FString, float> AnalyzeMaterialComplexity(UMaterial* Material, 
        const FString& TargetPlatform);
    static TArray<FString> GetMaterialShaderInstructions(UMaterial* Material, 
        const FString& TargetPlatform);
    static float CalculateMaterialRenderCost(UMaterial* Material, const FString& TargetPlatform);
    
    // Utility Functions
    static FString MaterialPathToPackageName(const FString& MaterialPath);
    static UMaterial* LoadMaterialFromPath(const FString& MaterialPath);
    static UMaterialInstanceConstant* LoadMaterialInstanceFromPath(const FString& InstancePath);
    static bool SaveMaterialAsset(UObject* Asset, const FString& PackagePath);
    static FString GetMaterialEditorErrorMessage(const FString& ErrorCode);
    
    // JSON Parsing Helpers
    static TSharedPtr<FJsonObject> ParseJsonParams(const FString& JsonParams);
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, 
        const TMap<FString, FString>& AdditionalData = TMap<FString, FString>());
    
    // Validation Functions
    static bool ValidateMaterialPath(const FString& MaterialPath);
    static bool ValidateExpressionType(const FString& ExpressionType);
    static bool ValidateParameterType(const FString& ParameterType);
    static bool ValidateHardwareTier(const FString& HardwareTier);
    static bool ValidateTargetPlatform(const FString& TargetPlatform);
    
    // Constants for Material System
    static const TArray<FString> SupportedShadingModels;
    static const TArray<FString> SupportedBlendModes;
    static const TArray<FString> SupportedExpressionTypes;
    static const TArray<FString> SupportedParameterTypes;
    static const TArray<FString> SupportedHardwareTiers;
    static const TArray<FString> SupportedTargetPlatforms;
};
